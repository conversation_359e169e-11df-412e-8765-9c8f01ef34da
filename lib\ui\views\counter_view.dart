import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import '../viewmodels/counter_viewmodel.dart';

class CounterView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<CounterViewModel>.reactive(
      viewModelBuilder: () => CounterViewModel(),
      builder: (context, viewModel, child) => Scaffold(
        appBar: AppBar(title: Text('Stacked Counter')),
        body: Center(
          child: Text('Counter: ${viewModel.counter}', style: TextStyle(fontSize: 24)),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: viewModel.incrementCounter,
          child: Icon(Icons.add),
        ),
      ),
    );
  }
}
